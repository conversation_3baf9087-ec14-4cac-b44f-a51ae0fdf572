package com.thedasagroup.suminative.ui.categories

import android.content.Intent
import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.CachePolicy
import coil.request.ImageRequest
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.compose.collectAsState
import com.airbnb.mvrx.launcher.MavericksLauncherActivity
import com.thedasagroup.suminative.BuildConfig
import com.thedasagroup.suminative.data.api.BASE_DOMAIN
import com.thedasagroup.suminative.data.model.response.options_details.OptionDetails
import com.thedasagroup.suminative.data.model.response.rewards.RewardStoreItem
import com.thedasagroup.suminative.data.model.response.stock.StockItem
import com.thedasagroup.suminative.ui.common.CartBottomBar
import com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersActivity
import com.thedasagroup.suminative.ui.local_orders.LocalOrdersActivity
import com.thedasagroup.suminative.ui.login.LoginActivity
import com.thedasagroup.suminative.ui.products.ProductsScreenState
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import com.thedasagroup.suminative.ui.refund.RefundSumUpActivity
import com.thedasagroup.suminative.ui.reservations.ReservationsActivity
import com.thedasagroup.suminative.ui.rewards.RewardsViewModel
import com.thedasagroup.suminative.ui.sales.SalesActivity
import com.thedasagroup.suminative.ui.stock.StockActivity
import com.thedasagroup.suminative.ui.stores.SelectStoreActivity
import com.thedasagroup.suminative.ui.theme.fontPoppins
import com.thedasagroup.suminative.ui.user_profile.SelectUserProfileActivity
import com.thedasagroup.suminative.ui.utils.transformDecimal
import ir.kaaveh.sdpcompose.sdp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@Composable
fun CategoryProductsScreen(
    categoryName: String,
    products: List<StockItem>,
    onBackClick: () -> Unit,
    onProductClick: (StockItem) -> Unit,
    viewModel: ProductsScreenViewModel,
    rewardsViewModel: RewardsViewModel,
    onAddToCart: (StockItem, OptionDetails) -> Unit,
    onOpenCart: () -> Unit = {},
    onAvailToCartSuccess : (RewardStoreItem?) -> Unit,
    onRemoveCustomer : () -> Unit,
    onRewardsClick: () -> Unit
) {
    var showOptions by remember { mutableStateOf(false) }
    var selectedProduct by remember { mutableStateOf<StockItem?>(null) }
    val coroutineScope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }
    
    // Get order state from ViewModel to display cart count
    val order by viewModel.collectAsState { it.getCurrentTableOrder() }
    val state by viewModel.collectAsState()
    val cartItemCount = order.carts?.size ?: 0
    
    Box(modifier = Modifier.fillMaxSize()) {
        Scaffold(
            snackbarHost = { 
                SnackbarHost(
                    hostState = snackbarHostState,
                    modifier = Modifier.padding(bottom = 60.dp)
                ) 
            }
        ) { paddingValues ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color(0xFFF5F5F5))
                    .statusBarsPadding()
                    .padding(paddingValues)
            ) {
                // Header with DASA branding, shopping bag, and hamburger menu
                DasaProductsHeader(
                    onBackClick = if (showOptions) {
                        { showOptions = false }
                    } else {
                        onBackClick
                    },
                    onOpenCart = onOpenCart,
                    cartItemCount = cartItemCount,
                    onRewardsClick = onRewardsClick,
                    viewModel = viewModel,
                )

                // Selected Customer Banner - Show table-specific or global customer
                val productsState by viewModel.collectAsState()
                val currentCustomer = productsState.getCurrentCustomer()
                if (currentCustomer != null) {
                    SelectedCustomerBanner(
                        customerName = currentCustomer.name ?: "Unknown Customer",
                        onRemove = {
                            onRemoveCustomer()
                        }
                    )
                }
                
                if (showOptions && selectedProduct != null) {
                    // Show product options
                    ProductOptionsScreen(
                        product = selectedProduct!!,
                        viewModel = viewModel,
                        onAddToCart = { stockItem, optionDetails ->
                                onAddToCart(stockItem, optionDetails)
                                // Navigate back to category page after adding to cart
                                onBackClick()
                            },
                        onBackClick = { showOptions = false }
                    )
                } else {
                    // Show products list
                    ProductsList(
                        categoryName = categoryName,
                        products = products,
                        onProductClick = { product ->
                            selectedProduct = product
                            coroutineScope.launch {
                                viewModel.getOptionDetails(product.id ?: 0, product)
                            }
                            showOptions = true
                        }
                    )
                }
            }
        }
        
        // Bottom Cart Bar
        if (cartItemCount > 0) {
            CartBottomBar(
                itemCount = cartItemCount,
                onCartClick = onOpenCart,
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
    }
}

@Composable
private fun ProductOptionsScreen(
    product: StockItem,
    viewModel: ProductsScreenViewModel,
    onAddToCart: (StockItem, OptionDetails) -> Unit,
    onBackClick: () -> Unit
) {
    val optionDetailsResponse by viewModel.collectAsState(ProductsScreenState::optionDetailsResponse)
    val productTotal by viewModel.collectAsState(ProductsScreenState::productTotal)
    
    LaunchedEffect(product.id) {
        withContext(Dispatchers.IO) {
            viewModel.resetStock()
            viewModel.getOptionDetails(product.id ?: 0, product)
        }
    }
    
    when {
        optionDetailsResponse is Loading || optionDetailsResponse is Uninitialized -> {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(color = Color(0xFF2E7D32))
            }
        }
        else -> {
            val optionDetails = optionDetailsResponse() ?: OptionDetails()
            val hasOptions = optionDetails.optionSets?.isNotEmpty() == true
            
            if (!hasOptions) {
                // No options, add directly to cart and navigate to category
                LaunchedEffect(Unit) {
                    onAddToCart(product, optionDetails)
                    // onBackClick() will now navigate to category page via the updated onAddToCart callback
                }
            } else {
                // Show options as individual product buttons
                ProductOptionsContent(
                    product = product,
                    optionDetails = optionDetails,
                    productTotal = productTotal,
                    onAddToCart = onAddToCart,
                    onBackClick = onBackClick
                )
            }
        }
    }
}

@Composable
private fun ProductOptionsContent(
    product: StockItem,
    optionDetails: OptionDetails,
    productTotal: Double,
    onAddToCart: (StockItem, OptionDetails) -> Unit,
    onBackClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        LazyColumn(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(12.dp, Alignment.CenterVertically)
        ) {
            // Show each option as a separate product button
            optionDetails.optionSets?.forEach { optionSet ->
                items(optionSet.options) { option ->
                    option?.let { opt ->
                        ProductOptionButton(
                            optionName = opt.name?.uppercase() ?: "UNKNOWN",
                            optionPrice = opt.price ?: 0.0,
                            basePrice = product.price ?: 0.0,
                            onClick = {
                                // Create updated option details with this option selected
                                val updatedOptionDetails = selectOption(optionDetails, optionSet.id ?: 0, opt.id ?: 0)
                                onAddToCart(product, updatedOptionDetails)
                                // onBackClick() will now navigate to category page via the updated onAddToCart callback
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun ProductOptionButton(
    optionName: String,
    optionPrice: Double,
    basePrice: Double,
    onClick: () -> Unit
) {
    val totalPrice = basePrice + optionPrice
    val showPrice = totalPrice > 0.0

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(68.dp)
            .clip(RoundedCornerShape(12.dp))
            .background(Color(0xFF2E7D32)) // Green background
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        if (showPrice) {
            // Show price layout with text on left and price on right
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = optionName,
                    color = Color.White,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    fontFamily = fontPoppins,
                    textAlign = TextAlign.Start,
                    modifier = Modifier.weight(1f)
                )
                
                Text(
                    text = "£${totalPrice.transformDecimal()}",
                    color = Color.White,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    fontFamily = fontPoppins,
                    textAlign = TextAlign.End
                )
            }
        } else {
            // Center the text when price is zero
            Text(
                text = optionName,
                color = Color.White,
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                fontFamily = fontPoppins,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

// Helper function to select an option and create updated OptionDetails
private fun selectOption(optionDetails: OptionDetails, optionSetId: Int, optionId: Int): OptionDetails {
    val updatedOptionSets = optionDetails.optionSets?.map { optionSet ->
        if (optionSet.id == optionSetId) {
            val updatedOptions = optionSet.options.map { option ->
                option?.copy(
                    optionchecked = option.id == optionId,
                    quantity = if (option.id == optionId) 1 else 0
                )
            }
            optionSet.copy(options = updatedOptions)
        } else {
            optionSet
        }
    }
    return optionDetails.copy(optionSets = updatedOptionSets)
}

@Composable
fun DasaProductsHeader(
    onBackClick: () -> Unit,
    showBack : Boolean = true,
    onOpenCart: () -> Unit = {},
    cartItemCount: Int = 0,
    onRewardsClick : () -> Unit,
    viewModel: ProductsScreenViewModel
) {
    val context = LocalContext.current
    var showMenu by remember { mutableStateOf(false) }
    var showLogoutDialog by remember { mutableStateOf(false) }
    val refreshing by viewModel.collectAsState(ProductsScreenState::refreshing)
    val coroutineScope = rememberCoroutineScope()
    
    // Logout confirmation dialog
    if (showLogoutDialog) {
        AlertDialog(
            onDismissRequest = { showLogoutDialog = false },
            title = { Text("Logout") },
            text = { Text("Are you sure you want to logout?") },
            confirmButton = {
                TextButton(
                    onClick = {
                        showLogoutDialog = false
                        viewModel.prefs.loginResponse = null
                        viewModel.prefs.store = null
                        val intent = Intent(context, LoginActivity::class.java)
                        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
                        context.startActivity(intent)
                    }
                ) {
                    Text("Yes", color = Color(0xFF2E7D32))
                }
            },
            dismissButton = {
                TextButton(onClick = { showLogoutDialog = false }) {
                    Text("No", color = Color(0xFF2E7D32))
                }
            }
        )
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White)
            .padding(horizontal = 16.dp, vertical = 12.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                if(showBack) {
                    IconButton(
                        onClick = onBackClick,
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back",
                            tint = Color(0xFF2E7D32)
                        )
                    }
                    Spacer(modifier = Modifier.width(8.dp))
                }
                val headerImage = viewModel.prefs.loginResponse?.businesses?.headerImage
                val imageUrl = "${BASE_DOMAIN}/dasa/streamer?name=${headerImage}"
                val request: ImageRequest =
                    ImageRequest.Builder(LocalContext.current.applicationContext).data(imageUrl)
                        .crossfade(true).diskCacheKey(imageUrl).diskCachePolicy(CachePolicy.ENABLED)
                        .setHeader("Cache-Control", "max-age=31536000").build()
                AsyncImage(
                    modifier = Modifier.size(50.sdp), model = request, contentDescription = ""
                )
            }
            
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // Cart icon button with badge
                Box {
                    IconButton(
                        onClick = onOpenCart,
                        modifier = Modifier.size(40.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.ShoppingCart,
                            contentDescription = "View Cart",
                            tint = Color(0xFF2E7D32),
                            modifier = Modifier.size(24.dp)
                        )
                    }

                    // Cart item count badge
                    if (cartItemCount > 0) {
                        Box(
                            modifier = Modifier
                                .size(20.dp)
                                .background(
                                    Color.Red,
                                    RoundedCornerShape(10.dp)
                                )
                                .align(Alignment.TopEnd),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = if (cartItemCount > 99) "99+" else cartItemCount.toString(),
                                color = Color.White,
                                fontSize = 10.sp,
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }
                }

                // Refresh button
                IconButton(
                    onClick = {
                        coroutineScope.launch {
                            withContext(Dispatchers.IO) {
                                viewModel.refreshProducts()
                            }
                        }
                    },
                    enabled = !refreshing
                ) {
                    if (refreshing) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            color = Color(0xFF2E7D32),
                            strokeWidth = 2.dp
                        )
                    } else {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = "Refresh Products",
                            tint = Color(0xFF2E7D32)
                        )
                    }
                }

                // Hamburger menu with dropdown
                Box {
                    IconButton(onClick = { showMenu = true }) {
                        Icon(
                            imageVector = Icons.Default.Menu,
                            contentDescription = "Menu",
                            tint = Color(0xFF2E7D32)
                        )
                    }
                    
                    DropdownMenu(
                        expanded = showMenu,
                        onDismissRequest = { showMenu = false },
                        modifier = Modifier.background(Color.White)
                    ) {
                        // Determine SumUp menu item based on login status
                        val sumUpMenuItem = if (com.thedasagroup.suminative.ui.payment.SumUpPaymentHelper.isLoggedIn()) "SumUp Logout" else "SumUp Login"
                        android.util.Log.d("CategoryProductsScreen", "Menu creation: SumUp isLoggedIn = ${com.thedasagroup.suminative.ui.payment.SumUpPaymentHelper.isLoggedIn()}")
                        android.util.Log.d("CategoryProductsScreen", "Menu creation: sumUpMenuItem = $sumUpMenuItem")

                        val menuItems = if (BuildConfig.DEBUG) {
                            listOf(
                                "Local Orders", "Change Store", "Stock Management", "Sales", "Refund", "Rewards","Logout", "Api Logs", "Payment Mocks", "Upload Logs", "Switch Waiter"
                            )
                        } else {
                            listOf(
                                "Local Orders", "Change Store", "Stock Management", "Sales", "Refund", "Rewards", "Logout", "Upload Logs"
                            )
                        }
                        
                        menuItems.forEach { item ->
                            DropdownMenuItem(
                                text = { 
                                    Text(
                                        text = item,
                                        color = Color.Black,
                                        fontFamily = fontPoppins
                                    ) 
                                },
                                onClick = {
                                    showMenu = false
                                    when (item) {
                                        "Local Orders" -> {
                                            val intent = Intent(context, LocalOrdersActivity::class.java)
                                            context.startActivity(intent)
                                        }
                                        "Logout" -> {
                                            showLogoutDialog = true
                                        }
                                        "Change Store" -> {
                                            viewModel.prefs.store = null
                                            val intent = Intent(context, SelectStoreActivity::class.java)
                                            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
                                            context.startActivity(intent)
                                        }
                                        "Stock Management" -> {
                                            val intent = Intent(context, StockActivity::class.java)
                                            context.startActivity(intent)
                                        }
                                        "Sales" -> {
                                            val intent = Intent(context, SalesActivity::class.java)
                                            context.startActivity(intent)
                                        }
                                        "Refund" -> {
                                            if(viewModel.isSumUp()){
                                                val intent = Intent(
                                                    context,
                                                    RefundSumUpActivity::class.java
                                                )
                                                context.startActivity(intent)
                                            }
                                            else {
                                                val intent = Intent(
                                                    context,
                                                    GuavaOrdersActivity::class.java
                                                )
                                                context.startActivity(intent)
                                            }
                                        }
                                        "SumUp Login" -> {
                                            android.util.Log.d("CategoryProductsScreen", "SumUp Login clicked")
                                            try {
                                                if (com.thedasagroup.suminative.ui.payment.SumUpPaymentHelper.isLoggedIn()) {
                                                    Toast.makeText(context, "Already logged in to SumUp", Toast.LENGTH_SHORT).show()
                                                } else {
                                                    android.util.Log.d("CategoryProductsScreen", "Starting SumUp login")
                                                    com.thedasagroup.suminative.ui.payment.SumUpPaymentHelper.startLogin(context as android.app.Activity)
                                                }
                                            } catch (e: Exception) {
                                                android.util.Log.e("CategoryProductsScreen", "Error with SumUp login", e)
                                                Toast.makeText(context, "SumUp login not available: ${e.message}", Toast.LENGTH_SHORT).show()
                                            }
                                        }
                                        "SumUp Logout" -> {
                                            android.util.Log.d("CategoryProductsScreen", "SumUp Logout clicked")
                                            try {
                                                if (com.thedasagroup.suminative.ui.payment.SumUpPaymentHelper.isLoggedIn()) {
                                                    com.thedasagroup.suminative.ui.payment.SumUpPaymentHelper.logout()
                                                    Toast.makeText(context, "Logged out from SumUp", Toast.LENGTH_SHORT).show()
                                                } else {
                                                    Toast.makeText(context, "Not logged in to SumUp", Toast.LENGTH_SHORT).show()
                                                }
                                            } catch (e: Exception) {
                                                android.util.Log.e("CategoryProductsScreen", "Error with SumUp logout", e)
                                                Toast.makeText(context, "SumUp logout not available: ${e.message}", Toast.LENGTH_SHORT).show()
                                            }
                                        }
                                        "Switch Waiter" -> {
                                            val intent = Intent(context, SelectUserProfileActivity::class.java)
                                            context.startActivity(intent)
                                        }
                                        "Upload Logs" -> {
                                            try {
                                                com.thedasagroup.suminative.work.LogUploadManager.uploadLogsNow(context)
                                                Toast.makeText(context, "Log upload requested", Toast.LENGTH_SHORT).show()
                                            } catch (e: Exception) {
                                                Toast.makeText(context, "Upload logs feature not available", Toast.LENGTH_SHORT).show()
                                            }
                                        }
                                        "Api Logs" -> {
                                            try {
                                                com.pluto.Pluto.open(com.pluto.plugins.network.PlutoNetworkPlugin.ID)
                                            } catch (e: Exception) {
                                                Toast.makeText(context, "API logs feature not available", Toast.LENGTH_SHORT).show()
                                            }
                                        }
                                        "Payment Mocks" -> {
                                            try {
                                                MavericksLauncherActivity.show(context = context)
                                            } catch (e: Exception) {
                                                Toast.makeText(context, "Payment mocks feature not available", Toast.LENGTH_SHORT).show()
                                            }
                                        }
                                        "Reservations" -> {
                                            val intent = Intent(context, ReservationsActivity::class.java)
                                            context.startActivity(intent)
                                        }
                                        "Rewards" -> {
                                           onRewardsClick()
                                        }
                                    }
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun ProductsList(
    categoryName: String,
    products: List<StockItem>,
    onProductClick: (StockItem) -> Unit
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5)),
        contentPadding = PaddingValues(start = 16.dp, end = 16.dp, top = 20.dp, bottom = 80.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp, Alignment.CenterVertically)
    ) {
        items(products) { product ->
            ProductButton(
                product = product,
                onClick = { onProductClick(product) }
            )
        }
    }
}

@Composable
private fun ProductButton(
    product: StockItem,
    onClick: () -> Unit
) {
    val borderColor = Color(0xFF003823)
    val price = product.price ?: 0.0
    val showPrice = price > 0.0

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(68.dp)
            .clip(RoundedCornerShape(12.dp))
            .background(Color.White)
            .clickable { onClick() }
            .padding(2.dp)
            .border(2.dp, borderColor, RoundedCornerShape(12.dp))
            .background(
                Color.White,
                RoundedCornerShape(10.dp)
            ),
        contentAlignment = Alignment.Center
    ) {
        if (showPrice) {
            // Show price layout with text on left and price on right
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = product.name?.uppercase() ?: "UNKNOWN",
                    color = Color(0xFF003823),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    fontFamily = fontPoppins,
                    textAlign = TextAlign.Start,
                    modifier = Modifier.weight(1f)
                )
                
                Text(
                    text = "£${price.transformDecimal()}",
                    color = Color(0xFF003823),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    fontFamily = fontPoppins,
                    textAlign = TextAlign.End
                )
            }
        } else {
            // Center the text when price is zero
            Text(
                text = product.name?.uppercase() ?: "UNKNOWN",
                color = Color(0xFF003823),
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                fontFamily = fontPoppins,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
} 
package com.thedasagroup.suminative.ui.products

// Data class for managing course status queues
data class CourseStatusQueue(
    val goQueue: List<String> = emptyList(),        // Courses with GO status in order
    val preparingQueue: List<String> = emptyList(), // Courses currently PREPARING (we will keep only current here)
    val completedCourses: List<String> = emptyList() // Courses that have been completed
) {
    /**
     * Get the next course that should have GO status
     */
    fun getNextGoCourse(): String? = goQueue.firstOrNull()

    /**
     * Get all courses currently preparing
     */
    fun getPreparingCourses(): List<String> = preparingQueue

    /**
     * Check if a course is in the GO queue
     */
    fun isInGoQueue(courseId: String): Boolean = goQueue.contains(courseId)

    /**
     * Check if a course is in the preparing queue
     */
    fun isInPreparingQueue(courseId: String): Boolean = preparingQueue.contains(courseId)

    /**
     * Check if a course is completed
     */
    fun isCompleted(courseId: String): Boolean = completedCourses.contains(courseId)

    /**
     * Check if a course should show as complete (must be explicitly in completed list)
     */
    fun shouldShowAsComplete(courseId: String): Boolean = isCompleted(courseId)

    /**
     * Move a course from GO queue to PREPARING, completing all previous and any existing preparing
     * This enforces a single current preparing course by replacing preparingQueue with [courseId]
     */
    fun moveFromGoToPreparingWithAllPreviousComplete(
        courseId: String,
        allCourses: List<String>
    ): CourseStatusQueue {
        val currentIndex = allCourses.indexOf(courseId)
        val previous = if (currentIndex > 0) allCourses.subList(0, currentIndex) else emptyList()

        // Any course currently in preparingQueue (other than this one) should be marked complete as well
        val preparingToComplete = preparingQueue.filter { it != courseId }

        return copy(
            goQueue = goQueue.filter { it != courseId },
            preparingQueue = listOf(courseId),
            completedCourses = (completedCourses + previous + preparingToComplete).distinct()
        )
    }

    /**
     * Add/remove helpers for queues
     */
    fun addToGoQueue(courseId: String): CourseStatusQueue = if (isInGoQueue(courseId)) this else copy(goQueue = goQueue + courseId)
    fun addToCompleteQueue(courseId: String): CourseStatusQueue = if (isInGoQueue(courseId)) this else copy(completedCourses = completedCourses + courseId)
    fun removeFromGoQueue(courseId: String): CourseStatusQueue = copy(goQueue = goQueue.filter { it != courseId })
    fun addToPreparingQueue(courseId: String): CourseStatusQueue = if (isInPreparingQueue(courseId)) this else copy(preparingQueue = listOf(courseId))
    fun removeFromPreparingQueue(courseId: String): CourseStatusQueue = copy(preparingQueue = preparingQueue.filter { it != courseId })

    fun removeFromCompleteQueue(courseId: String): CourseStatusQueue = copy(preparingQueue = completedCourses.filter { it != courseId })

    /**
     * Complete all preparing (single) course(s)
     */
    fun completeAllPreparingCourses(): CourseStatusQueue = copy(
        preparingQueue = emptyList(),
        completedCourses = (completedCourses + preparingQueue).distinct()
    )

    /**
     * Complete a specific course from preparing
     */
    fun completeSpecificCourse(courseId: String): CourseStatusQueue = if (isInPreparingQueue(courseId)) {
        copy(
            preparingQueue = preparingQueue.filter { it != courseId },
            completedCourses = (completedCourses + courseId).distinct()
        )
    } else this
}
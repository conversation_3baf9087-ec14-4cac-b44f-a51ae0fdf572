package com.thedasagroup.suminative.ui.products.cart

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AddCircle
import androidx.compose.material.icons.filled.FormatListBulleted
import androidx.compose.material.icons.filled.Money
import androidx.compose.material.icons.filled.RemoveCircle
import androidx.compose.material.icons.filled.Splitscreen
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.OutlinedButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.ui.products.ProductsScreenState
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import com.thedasagroup.suminative.ui.stores.isMobilePOS
import com.thedasagroup.suminative.ui.theme.fontPoppins
import com.thedasagroup.suminative.ui.utils.transformDecimal
import com.thedasagroup.suminative.ui.utils.transformDecimal1

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun BillTabContent(
    order: Order,
    state: ProductsScreenState,
    onVoidItem: (Cart) -> Unit,
    onCloudPrintClick: (Order) -> Unit,
    onApplyServiceChargeClick: () -> Unit,
    onRemoveServiceChargeClick: () -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {

    Box(modifier = Modifier.fillMaxSize()) {
        // Scrollable content
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
            contentPadding = PaddingValues(top = 16.dp, bottom = 300.dp) // Bottom padding for bill summary
        ) {
            // Order Summary Header
            item {
                Text(
                    text = "BILL SUMMARY (${order.carts?.size ?: 0} items)",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black,
                    fontFamily = fontPoppins,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            // Order Items List
            order.carts?.let { carts ->
                items(carts) { cartItem ->
                    PayTabOrderItem(
                        cartItem = cartItem,
                        onVoidItem = onVoidItem,
                        productsScreenViewModel = productsScreenViewModel
                    )
                }
            }
        }

        // Sticky Bill Summary Section at the bottom
        BillTabPaymentSection(
            order = order,
            state = state,
            onPrintBill = {
                onCloudPrintClick(order)
            },
            productsScreenViewModel = productsScreenViewModel,
            onApplyServiceChargeClick = {
                onApplyServiceChargeClick()
            },
            onRemoveServiceChargeClick = onRemoveServiceChargeClick,
            modifier = Modifier.align(Alignment.BottomCenter)
        )
    }
}

@Composable
private fun BillTabPaymentSection(
    order: Order,
    state: ProductsScreenState,
    onPrintBill: () -> Unit,
    onApplyServiceChargeClick: () -> Unit,
    onRemoveServiceChargeClick: () -> Unit,
    productsScreenViewModel: ProductsScreenViewModel,
    modifier: Modifier = Modifier
) {

    val orderResponse by productsScreenViewModel.collectAsState(ProductsScreenState::orderResponse)

    androidx.compose.material3.Card(
        modifier = modifier
            .fillMaxWidth()
            .height(300.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Payment Options Header
            Text(
                text = "Bill Options",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black,
                fontFamily = fontPoppins
            )

            // Payment Buttons Row
            when (orderResponse) {

                is Loading -> {
                    Box(
                        modifier = Modifier.fillMaxWidth(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            color = Color(0xFF2E7D32)
                        )
                    }
                }

                else -> {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        ApplyServiceChargeButton(
                            order = order, productsScreenViewModel = productsScreenViewModel,
                            onApplyServiceChargeClick = onApplyServiceChargeClick,
                            onRemoveServiceChargeClick = onRemoveServiceChargeClick,
                            modifier = Modifier
                                .fillMaxWidth(0.4f)
                                .height(64.dp)
                        )

                        // Card Button
                        Button(
                            onClick = onPrintBill,
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(64.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF2E7D32),
                                contentColor = Color.White
                            ),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            if (!isMobilePOS) {
                                Icon(
                                    imageVector = Icons.Default.FormatListBulleted,
                                    contentDescription = null,
                                    modifier = Modifier.size(18.dp),
                                    tint = Color.White
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                            }
                            Text(
                                text = "Print Bill",
                                fontWeight = FontWeight.Bold,
                                fontSize = 14.sp,
                                fontFamily = fontPoppins,
                                color = Color.White
                            )
                        }
                    }
                }
            }
            Spacer(modifier = Modifier.height(8.dp))
            BillTabOrderSummary(
                order = order,
                state = state,
                productsScreenViewModel = productsScreenViewModel
            )
        }
    }
}

@Composable
private fun ApplyServiceChargeButton(
    order: Order,
    onApplyServiceChargeClick: () -> Unit,
    onRemoveServiceChargeClick: () -> Unit,
    productsScreenViewModel: ProductsScreenViewModel,
    modifier: Modifier
) {
    val state by productsScreenViewModel.collectAsState()
    val serviceChargeApplied = state.isServiceChargeApplied()
    val serviceChargePercentage = productsScreenViewModel.getServiceChargePercentage()
    if (serviceChargeApplied) {
        // Remove Service Charge Button
        androidx.compose.material.OutlinedButton(
            onClick = { onRemoveServiceChargeClick() },
            modifier = modifier
                .height(64.dp),
            shape = RoundedCornerShape(8.dp),
            border = BorderStroke(1.dp, Color.Red),
            colors = androidx.compose.material.ButtonDefaults.outlinedButtonColors(
                contentColor = Color.Red
            )
        ) {
            Icon(
                imageVector = Icons.Default.RemoveCircle,
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "Service Charge (${serviceChargePercentage.transformDecimal()}%)",
                fontWeight = FontWeight.Bold,
                fontSize = 14.sp,
                fontFamily = fontPoppins,
            )
        }
    } else {
        // Apply Service Charge Button
        OutlinedButton(
            onClick = { onApplyServiceChargeClick() },
            modifier = modifier
                .height(64.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFF2E7D32),
                contentColor = Color.White
            ),
            shape = RoundedCornerShape(8.dp)
        ) {
            Icon(
                imageVector = Icons.Default.AddCircle,
                contentDescription = null,
                modifier = Modifier.size(18.dp),
                tint = Color.White
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "Service Charge (${serviceChargePercentage.transformDecimal()}%)",
                fontWeight = FontWeight.Bold,
                fontSize = 14.sp,
                fontFamily = fontPoppins,
                color = Color.White
            )
        }
    }
}

@Composable
private fun BillTabOrderSummary(
    order: Order,
    state: ProductsScreenState,
    productsScreenViewModel: ProductsScreenViewModel
) {
    val serviceCharge = state.getServiceChargeAmount(
        productsScreenViewModel.prefs.storeConfigurations?.data?.serviceChargePercentage ?: 0.0
    )

    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp),
        modifier = Modifier.padding(bottom = if(isMobilePOS) 30.dp else 0.dp)
    ) {
        // Sub Total
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Sub Total",
                fontSize = 16.sp,
                fontWeight = FontWeight.Normal,
                color = Color.Black,
                fontFamily = fontPoppins
            )
            Text(
                text = "£${order.netPayable?.transformDecimal() ?: "0.00"}",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black,
                fontFamily = fontPoppins
            )
        }

        // Tax
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Service Charge (${productsScreenViewModel.prefs.storeConfigurations?.data?.serviceChargePercentage?.transformDecimal1() ?: 0.0}%)",
                fontSize = 16.sp,
                fontWeight = FontWeight.Normal,
                color = Color.Black,
                fontFamily = fontPoppins
            )
            Text(
                text = "£${serviceCharge.transformDecimal()}",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black,
                fontFamily = fontPoppins
            )
        }

        // Total Payable with green background
        androidx.compose.material3.Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(8.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFF2E7D32)
            ),
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Total Payable",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    fontFamily = fontPoppins
                )
                Text(
                    text = "£${order.totalPrice?.transformDecimal() ?: "0.00"}",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    fontFamily = fontPoppins
                )
            }
        }
    }
}
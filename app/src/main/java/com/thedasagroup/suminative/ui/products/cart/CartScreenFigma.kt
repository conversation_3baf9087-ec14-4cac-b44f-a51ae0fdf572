package com.thedasagroup.suminative.ui.products.cart

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.TextField
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AddCard
import androidx.compose.material.icons.filled.AddCircle
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Money
import androidx.compose.material.icons.filled.RemoveCircle
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.R
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.StoreItem
import com.thedasagroup.suminative.ui.theme.fontNunito
import com.thedasagroup.suminative.ui.theme.fontPoppins
import com.thedasagroup.suminative.ui.utils.transformDecimal
import androidx.compose.ui.window.Dialog
import androidx.compose.material3.AlertDialog
import com.thedasagroup.suminative.data.model.response.options_details.OptionDetails
import com.thedasagroup.suminative.ui.products.CATEGORY_GREEN_COLOR
import com.thedasagroup.suminative.ui.products.MealCourse
import com.thedasagroup.suminative.ui.products.CartItemWithCourse
import com.thedasagroup.suminative.ui.products.ProductsScreenState
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import com.thedasagroup.suminative.ui.products.shouldShowComplete
import com.thedasagroup.suminative.ui.products.shouldShowGoButton
import com.thedasagroup.suminative.ui.products.shouldShowPreparing
import com.thedasagroup.suminative.ui.stores.isMobilePOS
import kotlin.collections.forEach
import kotlin.let
import kotlin.text.isNotEmpty
import kotlin.text.uppercase

@Composable
fun CartScreenFigma(
    order: Order,
    state: ProductsScreenState,
    onRemoveItem: (Cart) -> Unit,
    closeCart: () -> Unit,
    placeOrderCash: () -> Unit,
    placeOrderCard: () -> Unit,
    onUpdateStock: (Int, StoreItem, Cart, OptionDetails?) -> Unit,
    onUpdateNotes: (Cart, String) -> Unit,
    onVoidItem: (Cart) -> Unit,
    onSplitBillClick: (Int) -> Unit,
    onCloudPrintClick: (Order) -> Unit,
    onAddNewCourse: (String) -> Unit,
    onCourseBillClick: (Order) -> Unit,
    onGoButtonClick: (String) -> Unit,
    onCompleteButtonClick: (String) -> Unit,
    onSendToKitchen: () -> Unit,
    onApplyServiceChargeClick: (Order) -> Unit,
    onRemoveServiceChargeClick: (Order) -> Unit,
    onCrossClick: () -> Unit,
    onSyncTable: () -> Unit,
    onAddNumberedCourse: () -> Unit,
    onUpdateCartTab : (CartTab) -> Unit,
    openPrefillFromCart: (Cart) -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    MobileCartScreen(
        order = order,
        onRemoveItem = onRemoveItem,
        closeCart = closeCart,
        placeOrderCash = placeOrderCash,
        placeOrderCard = placeOrderCard,
        onUpdateStock = { stock, storeItem, cart ->
            // Create OptionDetails from the cart item's store item option sets
            val cartOptionDetails =
                OptionDetails(optionSets = storeItem.optionSets ?: mutableListOf())
            onUpdateStock(stock, storeItem, cart, cartOptionDetails)
        },
        onUpdateNotes = onUpdateNotes,
        onSplitBillClick = onSplitBillClick,
        onVoidItem = onVoidItem,
        onCloudPrintClick = onCourseBillClick,
        onAddNewCourse = onAddNewCourse,
        productsScreenViewModel = productsScreenViewModel,
        onCourseBillClick = onCourseBillClick,
        onGoButtonClick = onGoButtonClick,
        onSendToKitchen = onSendToKitchen,
        onApplyServiceChargeClick = onApplyServiceChargeClick,
        onRemoveServiceChargeClick = onRemoveServiceChargeClick,
        onCrossClick = onCrossClick,
        onSyncTable = onSyncTable,
        onAddNumberedCourse = onAddNumberedCourse,
        onCompleteButtonClick = onCompleteButtonClick,
        onUpdateCartTab = onUpdateCartTab,
        openPrefillFromCart = openPrefillFromCart,
        state = state
    )
}

// Enum for main cart tabs
enum class CartTab(val displayName: String) {
    ORDER("Order"),
    BILL("Bill"),
    PAY("Pay")
}

// Enum for course status
enum class CourseStatus(val displayName: String) {
    GO("Go"),
    PREPARING("Preparing"),
    COMPLETE("Complete")
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun MobileCartScreen(
    order: Order,
    state: ProductsScreenState,
    onRemoveItem: (Cart) -> Unit,
    closeCart: () -> Unit,
    placeOrderCash: () -> Unit,
    placeOrderCard: () -> Unit,
    onUpdateStock: (Int, StoreItem, Cart) -> Unit,
    onUpdateNotes: (Cart, String) -> Unit,
    onSplitBillClick: (Int) -> Unit,
    onCloudPrintClick: (Order) -> Unit,
    onVoidItem: (Cart) -> Unit,
    onAddNewCourse: (String) -> Unit,
    onCourseBillClick: (Order) -> Unit,
    onGoButtonClick: (String) -> Unit,
    onSendToKitchen: () -> Unit,
    onApplyServiceChargeClick: (Order) -> Unit,
    onRemoveServiceChargeClick: (Order) -> Unit,
    onCrossClick: () -> Unit,
    onSyncTable: () -> Unit,
    onAddNumberedCourse: () -> Unit,
    onCompleteButtonClick: (String) -> Unit,
    onUpdateCartTab: (CartTab) -> Unit,
    openPrefillFromCart: (Cart) -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {

    val selectedTab = state.getSelectedCartTab()

    // State for edit courses dialog
    var showEditCoursesDialog by remember { mutableStateOf(false) }
    var showLastCourseAlert by remember { mutableStateOf(false) }

    // Get state from ViewModel using Mavericks
    val availableCourses = state.getCurrentTableAvailableCourses()
    val cartItemsWithCourses = state.getCurrentTableCartItemsWithCourses()

    // Sync course assignments when order changes
    LaunchedEffect(order.carts) {
        productsScreenViewModel.syncCartItemsWithCourses(state = state)
    }

    // Initialize the first course as active when cart opens
    LaunchedEffect(Unit) {
        productsScreenViewModel.initializeActiveCourse()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        // Main tabs (Order, Bill, Pay)
        MainCartTabs(
            selectedTab = selectedTab,
            onCrossClick = onCrossClick,
            onTabSelected = {
                onUpdateCartTab(it)
            }
        )

        // Content based on selected tab
        when (selectedTab) {
            CartTab.ORDER -> {
                OrderTabContent(
                    order = order,
                    cartItemsWithCourses = cartItemsWithCourses,
                    availableCourses = availableCourses,
                    onRemoveItem = onRemoveItem,
                    onUpdateStock = onUpdateStock,
                    onUpdateNotes = onUpdateNotes,
                    onVoidItem = onVoidItem,
                    onAddNewCourse = onAddNewCourse,
                    onCourseBillClick = onCourseBillClick,
                    onGoButtonClick = onGoButtonClick,
                    onCompleteButtonClick = onCompleteButtonClick,
                    onSendToKitchen = onSendToKitchen,
                    onShowEditCoursesDialog = {
                        showEditCoursesDialog = true
                    },
                    onAddNumberedCourse = onAddNumberedCourse,
                    state = state,
                    onSyncTable = onSyncTable,
                    openPrefillFromCart = openPrefillFromCart,
                    productsScreenViewModel = productsScreenViewModel
                )
            }

            CartTab.BILL -> {
                BillTabContent(
                    order = order,
                    state = state,
                    onVoidItem = onVoidItem,
                    onCloudPrintClick = onCloudPrintClick,
                    onApplyServiceChargeClick = {
                        onApplyServiceChargeClick(order)
                    },
                    onRemoveServiceChargeClick = {
                        onRemoveServiceChargeClick(order)
                    },
                    productsScreenViewModel = productsScreenViewModel
                )
            }

            CartTab.PAY -> {
                PayTabContent(
                    state = state,
                    order = order,
                    placeOrderCard = placeOrderCard,
                    placeOrderCash = placeOrderCash,
                    onSplitBillClick = onSplitBillClick,
                    onCloudPrintClick = onCloudPrintClick,
                    onVoidItem = onVoidItem,
                    onSyncTable = onSyncTable,
                    productsScreenViewModel = productsScreenViewModel
                )
            }
        }
    }

    // Edit Courses Dialog
    if (showEditCoursesDialog) {
        EditCoursesDialog(
            availableCourses = availableCourses,
            onDismiss = { showEditCoursesDialog = false },
            onAddCourse = { courseName ->
                productsScreenViewModel.addNewCourse(
                    courseName,
                    availableCourses = availableCourses
                )
            },
            onEditCourse = { courseId, newName ->
                productsScreenViewModel.editCourse(courseId, newName)
            },
            onRemoveCourse = { courseId ->
                val removed = productsScreenViewModel.removeCourse(courseId, state = state)
                if (!removed) {
                    showLastCourseAlert = true
                }
            }
        )
    }

    // Last Course Alert Dialog
    if (showLastCourseAlert) {
        androidx.compose.material3.AlertDialog(
            onDismissRequest = { showLastCourseAlert = false },
            title = {
                Text(
                    text = "Cannot Remove Course",
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32)
                )
            },
            text = {
                Text(
                    text = "You cannot remove the last course. At least one course must remain.",
                    color = Color.Black
                )
            },
            confirmButton = {
                androidx.compose.material3.TextButton(
                    onClick = { showLastCourseAlert = false }
                ) {
                    Text(
                        text = "OK",
                        color = Color(0xFF2E7D32),
                        fontWeight = FontWeight.Bold
                    )
                }
            },
            containerColor = Color.White,
            titleContentColor = Color(0xFF2E7D32),
            textContentColor = Color.Black
        )
    }
}

@Composable
private fun MainCartTabs(
    selectedTab: CartTab,
    onTabSelected: (CartTab) -> Unit,
    onCrossClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {

        androidx.compose.material3.IconButton(
            onClick = {
                onCrossClick()
            },
            enabled = true,
            modifier = Modifier
                .background(
                    Color(0xFF2E7D32),
                    shape = CircleShape
                )
        ) {
            Icon(
                imageVector = Icons.Filled.Close,
                contentDescription = "Close Cart",
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
        }

        Spacer(modifier = Modifier.width(8.dp))

        // Order and Pay tabs
        CartTab.values().forEach { tab ->
            val isSelected = tab == selectedTab
            val backgroundColor = if (isSelected) Color(0xFF2E7D32) else Color.Transparent
            val textColor = if (isSelected) Color.White else Color.Black
            val borderColor = Color(0xFF2E7D32)

            androidx.compose.material3.Card(
                modifier = Modifier
                    .weight(1f)
                    .clickable { onTabSelected(tab) }
                    .border(
                        width = 1.dp,
                        color = borderColor,
                        shape = RoundedCornerShape(8.dp)
                    ),
                shape = RoundedCornerShape(8.dp),
                colors = CardDefaults.cardColors(containerColor = backgroundColor),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
            ) {
                Text(
                    text = tab.displayName,
                    color = textColor,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    fontFamily = fontPoppins,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 12.dp),
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
private fun OrderTabContent(
    order: Order,
    cartItemsWithCourses: List<CartItemWithCourse>,
    availableCourses: List<MealCourse>,
    onRemoveItem: (Cart) -> Unit,
    onUpdateStock: (Int, StoreItem, Cart) -> Unit,
    onUpdateNotes: (Cart, String) -> Unit,
    onVoidItem: (Cart) -> Unit,
    onAddNewCourse: (String) -> Unit = {},
    onCourseBillClick: (Order) -> Unit,
    onGoButtonClick: (String) -> Unit,
    onCompleteButtonClick: (String) -> Unit,
    onSendToKitchen: () -> Unit,
    onShowEditCoursesDialog: () -> Unit,
    state: ProductsScreenState,
    onSyncTable: () -> Unit,
    onAddNumberedCourse: () -> Unit,
    openPrefillFromCart: (Cart) -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    val courseStatuses by productsScreenViewModel.collectAsState(ProductsScreenState::courseStatuses)
    val selectedCourseForNewItems by productsScreenViewModel.collectAsState { it.getCurrentTableSelectedCourseForNewItems() }
    Box(modifier = Modifier.fillMaxSize()) {
        // Scrollable content
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(
                top = 16.dp,
                bottom = 200.dp
            ) // Bottom padding for sticky summary
        ) {

            item {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {

                    // Course + button and name
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Button(
                            onClick = {
                                onSendToKitchen()
                            },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF2E7D32),
                                contentColor = Color.White
                            ),
                            shape = RoundedCornerShape(6.dp),
                            modifier = Modifier.height(32.dp),
                            contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
                        ) {
                            Text(
                                text = "Send To Kitchen",
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium,
                                fontFamily = fontPoppins,
                                color = Color.White
                            )
                        }

                        Button(
                            onClick = {
                                onAddNumberedCourse()
                            },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF2E7D32),
                                contentColor = Color.White
                            ),
                            shape = RoundedCornerShape(6.dp),
                            modifier = Modifier.height(32.dp),
                            contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
                        ) {
                            Text(
                                text = "Course +",
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium,
                                fontFamily = fontPoppins,
                                color = Color.White
                            )
                        }

//                        Button(
//                            onClick = {
//                                productsScreenViewModel.addDefaultCourses()
//                            },
//                            colors = ButtonDefaults.buttonColors(
//                                containerColor = Color(0xFF2E7D32),
//                                contentColor = Color.White
//                            ),
//                            shape = RoundedCornerShape(6.dp),
//                            modifier = Modifier.height(32.dp),
//                            contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
//                        ) {
//                            Text(
//                                text = "Default Courses +",
//                                fontSize = 10.sp,
//                                fontWeight = FontWeight.Medium,
//                                fontFamily = fontPoppins,
//                                color = Color.White
//                            )
//                        }

                        Button(
                            onClick = {
                                onShowEditCoursesDialog()
                            },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF2E7D32),
                                contentColor = Color.White
                            ),
                            shape = RoundedCornerShape(6.dp),
                            modifier = Modifier.height(32.dp),
                            contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
                        ) {
                            Text(
                                text = "Edit Courses",
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium,
                                fontFamily = fontPoppins,
                                color = Color.White
                            )
                        }
                    }
                }
            }

//            item {
//                // Sync Table Button
//                Button(
//                    onClick = {
//                        onSyncTable()
//                    },
//                    colors = ButtonDefaults.buttonColors(
//                        containerColor = Color(0xFF2E7D32),
//                        contentColor = Color.White
//                    ),
//                    shape = RoundedCornerShape(6.dp),
//                    modifier = Modifier.height(32.dp),
//                    contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
//                ) {
//                    Text(
//                        text = "Sync",
//                        fontSize = 12.sp,
//                        fontWeight = FontWeight.Medium,
//                        fontFamily = fontPoppins,
//                        color = Color.White
//                    )
//                }
//
//                Spacer(modifier = Modifier.height(12.dp))
//            }
            // Show all courses
            availableCourses.sortedBy { it.sortOrder }.forEach { course ->
                val itemsInCourse = cartItemsWithCourses.filter { it.courseId == course.name }
                val itemCount = itemsInCourse.size
                val isSelectedCourse = selectedCourseForNewItems == course.name

                // Course header with Go button
                item {
                    CourseHeader(
                        courseName = course.name,
                        itemCount = itemCount,
                        status = productsScreenViewModel.getCourseStatus(
                            course.name,
                            state = state
                        ),
                        courseId = course.name,
                        isSelectedCourse = isSelectedCourse,
                        onAddNewCourse = { onAddNumberedCourse() },
                        onSelectCourse = { courseId ->
                            productsScreenViewModel.setSelectedCourseForNewItems(courseId)
                        },
                        onGoButtonClick = { courseId ->
                            onGoButtonClick(courseId)
                        },
                        state = state,
                        onCompleteButtonClick = onCompleteButtonClick,
                        productsScreenViewModel = productsScreenViewModel
                    )
                }

                // Course items (if any)
                if (itemsInCourse.isNotEmpty()) {
                    items(
                        items = itemsInCourse,
                        key = { cartItemWithCourse ->
                            "${cartItemWithCourse.cart.storeItem?.id ?: ""}_${cartItemWithCourse.cart.storeItem?.uuid}_${cartItemWithCourse.cart.quantity}_${cartItemWithCourse.courseId}_${cartItemWithCourse.cart.notes ?: ""}_${cartItemWithCourse.cart.storeItem?.optionsKey() ?: ""}_${cartItemWithCourse.cart.storeItem?.courseId ?: ""}"
                        }
                    ) { cartItemWithCourse ->
                        OrderItemCard(
                            cartItemWithCourse = cartItemWithCourse,
                            availableCourses = availableCourses,
                            onRemoveItem = onRemoveItem,
                            onUpdateStock = onUpdateStock,
                            onUpdateNotes = onUpdateNotes,
                            onVoidItem = onVoidItem,
                            onCourseChanged = { newCourseId ->
                                productsScreenViewModel.updateCartItemCourse(
                                    cartUuid = cartItemWithCourse.cart.uuid,
                                    newCourseId = newCourseId
                                )
                            },
                            openPrefillFromCart = openPrefillFromCart,
                            productsScreenViewModel = productsScreenViewModel
                        )
                    }
                } else {
                    // Show empty state for this course
                    item {
                        Text(
                            text = "No items in ${course.name}",
                            fontSize = 14.sp,
                            color = Color.Gray,
                            fontFamily = fontPoppins,
                            modifier = Modifier.padding(vertical = 8.dp, horizontal = 16.dp)
                        )
                    }
                }
            }

            // Show uncategorized items (items with empty courseId or courseId not matching any available course)
            // Only show uncategorized section when there are courses available
//            val uncategorizedItems = cartItemsWithCourses.filter { item ->
//                item.courseId.isEmpty() || availableCourses.none { course -> course.name == item.courseId }
//            }
//
//            if (uncategorizedItems.isNotEmpty() && availableCourses.isNotEmpty()) {
//                // Uncategorized items header
//                item {
//                    Row(
//                        modifier = Modifier
//                            .fillMaxWidth()
//                            .padding(vertical = 4.dp),
//                        horizontalArrangement = Arrangement.SpaceBetween,
//                        verticalAlignment = Alignment.CenterVertically
//                    ) {
//                        Text(
//                            text = "Uncategorized (${uncategorizedItems.size} items)",
//                            fontSize = 18.sp,
//                            fontWeight = FontWeight.Bold,
//                            color = Color.Black,
//                            fontFamily = fontPoppins
//                        )
//                    }
//                }
//
//                // Uncategorized items
//                items(
//                    items = uncategorizedItems,
//                    key = { cartItemWithCourse ->
//                        "${cartItemWithCourse.cart.storeItem?.id ?: ""}_${cartItemWithCourse.cart.quantity}_${cartItemWithCourse.courseId}_${cartItemWithCourse.cart.notes ?: ""}_${cartItemWithCourse.cart.storeItem?.optionsKey() ?: ""}_${cartItemWithCourse.cart.storeItem?.courseId ?: ""}"
//                    }
//                ) { cartItemWithCourse ->
//                    OrderItemCard(
//                        cartItemWithCourse = cartItemWithCourse,
//                        availableCourses = availableCourses,
//                        onRemoveItem = onRemoveItem,
//                        onUpdateStock = onUpdateStock,
//                        onUpdateNotes = onUpdateNotes,
//                        onVoidItem = onVoidItem,
//                        onCourseChanged = { newCourseId ->
//                            productsScreenViewModel.updateCartItemCourse(
//                                cartUuid = cartItemWithCourse.cart.uuid,
//                                newCourseId = newCourseId
//                            )
//                        },
//                        productsScreenViewModel = productsScreenViewModel
//                    )
//                }
//            }

            // Show items without course labels when no courses are available
            if (availableCourses.isEmpty() && cartItemsWithCourses.isNotEmpty()) {
                items(
                    items = cartItemsWithCourses,
                    key = { cartItemWithCourse ->
                        "${cartItemWithCourse.cart.storeItem?.id ?: ""}_${cartItemWithCourse.cart.quantity}_${cartItemWithCourse.courseId}_${cartItemWithCourse.cart.notes ?: ""}_${cartItemWithCourse.cart.storeItem?.optionsKey() ?: ""}_${cartItemWithCourse.cart.storeItem?.courseId ?: ""}"
                    }
                ) { cartItemWithCourse ->
                    OrderItemCard(
                        cartItemWithCourse = cartItemWithCourse,
                        availableCourses = availableCourses,
                        onRemoveItem = onRemoveItem,
                        onUpdateStock = onUpdateStock,
                        onUpdateNotes = onUpdateNotes,
                        onVoidItem = onVoidItem,
                        onCourseChanged = { newCourseId ->
                            productsScreenViewModel.updateCartItemCourse(
                                cartUuid = cartItemWithCourse.cart.uuid,
                                newCourseId = newCourseId
                            )
                        },
                        openPrefillFromCart = openPrefillFromCart,
                        productsScreenViewModel = productsScreenViewModel
                    )
                }
            }

            // Show overall empty cart message if no items at all
            if (cartItemsWithCourses.isEmpty()) {
                item {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(32.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        androidx.compose.material3.Icon(
                            painter = painterResource(R.drawable.cart_plus),
                            contentDescription = "Empty cart",
                            modifier = Modifier.size(80.dp),
                            tint = Color(0xFF2E7D32).copy(alpha = 0.6f)
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Text(
                            text = "Your cart is empty",
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.Black,
                            fontFamily = fontPoppins
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = if (availableCourses.isEmpty()) "Add items to get started or click 'Course +' to organize with courses" else "Add items to get started",
                            fontSize = 16.sp,
                            color = Color.Black.copy(alpha = 0.7f),
                            fontFamily = fontPoppins,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }

        // Sticky Order Summary at the bottom (only show if there are items)
        if (cartItemsWithCourses.isNotEmpty()) {
            Box(
                modifier = Modifier
                    .background(color = Color.White)
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
            ) {
                androidx.compose.material3.Card(
                    modifier = Modifier
                        .background(color = Color.White)
                        .fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    shape = RoundedCornerShape(12.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .background(color = Color.White)
                            .padding(16.dp)
                    ) {
                        OrderSummarySection(
                            order = order,
                            onCourseBillClick = onCourseBillClick
                        )
                    }
                }
            }
        }
    }
}


@Composable
private fun CourseHeader(
    courseName: String,
    itemCount: Int,
    status: CourseStatus,
    courseId: String,
    isSelectedCourse: Boolean = false,
    onAddNewCourse: () -> Unit = {},
    onSelectCourse: (String) -> Unit = {},
    onGoButtonClick: (String) -> Unit = {},
    onCompleteButtonClick: (String) -> Unit,
    state: ProductsScreenState,
    productsScreenViewModel: ProductsScreenViewModel
) {
    var showDeleteCourseDialog by remember { mutableStateOf(false) }

    // Check if any items in this course have been sent to kitchen
    val currentTableId = state.getCurrentTableId()
    val cartItemsInCourse = if (currentTableId != null) {
        state.cartItemsWithCourses[currentTableId]?.filter { it.courseId == courseId } ?: emptyList()
    } else {
        state.globalCartItemsWithCourses.filter { it.courseId == courseId }
    }
    val hasItemsSentToKitchen = cartItemsInCourse.any { it.cart.sentToKitchen }

    // Course title with item count and delete button
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "$courseName ($itemCount items)",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black,
                fontFamily = fontPoppins
            )

            // Delete course icon button - only show if no items sent to kitchen
            if (!hasItemsSentToKitchen) {
                androidx.compose.material3.IconButton(
                    onClick = { showDeleteCourseDialog = true },
                    modifier = Modifier.size(32.dp)
                ) {
                    androidx.compose.material3.Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "Delete course",
                        tint = Color.Red,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }

        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Select button
            Button(
                onClick = { onSelectCourse(courseId) },
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (isSelectedCourse) Color(0xFF1B5E20) else Color(0xFF2E7D32),
                    contentColor = Color.White
                ),
                shape = RoundedCornerShape(6.dp),
                modifier = Modifier.height(32.dp),
                contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
            ) {
                Text(
                    text = if (isSelectedCourse) "Selected" else "Select",
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    fontFamily = fontPoppins,
                    color = Color.White
                )
            }

            // Only show Go button for the active course
            val shouldShowGo = shouldShowGoButton(
                courseId = courseId,
                state = state
            )

            val shouldShowPreparing = shouldShowPreparing(
                courseId = courseId,
                state = state
            )
            val shouldShowCompleteButton = productsScreenViewModel.shouldShowCompleteButton(
                courseId = courseId,
                state = state
            )

            // Status-based Go button or text
            when {
                shouldShowGo -> {
                    Button(
                        onClick = { onGoButtonClick(courseId) },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2E7D32),
                            contentColor = Color.White
                        ),
                        shape = RoundedCornerShape(6.dp),
                        modifier = Modifier.height(32.dp),
                        contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
                    ) {
                        Text(
                            text = "Go",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            fontFamily = fontPoppins,
                            color = Color.White
                        )
                    }
                }
                shouldShowPreparing -> {
                    if(shouldShowCompleteButton){
                        Row {
                            Text(
                                text = "Preparing",
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium,
                                fontFamily = fontPoppins,
                                color = Color(0xFFFF9800), // Orange color for preparing
                                modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Button(
                                onClick = { onCompleteButtonClick(courseId) },
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFF2E7D32),
                                    contentColor = Color.White
                                ),
                                shape = RoundedCornerShape(6.dp),
                                modifier = Modifier.height(32.dp),
                                contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
                            ) {
                                Text(
                                    text = "Complete",
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Medium,
                                    fontFamily = fontPoppins,
                                    color = Color.White
                                )
                            }
                        }
                    }
                    else {
                        Text(
                            text = "Preparing",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            fontFamily = fontPoppins,
                            color = Color(0xFFFF9800), // Orange color for preparing
                            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp)
                        )
                    }
                }

                else -> {
                    // For all other statuses (COMPLETE, WAITING, etc.), determine if should show Complete or Waiting
                    val shouldShowCompleteStatus = shouldShowComplete(
                        courseId = courseId,
                        state = state
                    )

                    if (shouldShowCompleteStatus) {
                        Text(
                            text = "Complete",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            fontFamily = fontPoppins,
                            color = Color(0xFF4CAF50), // Green color for complete
                            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp)
                        )
                    } else {
                        // Show waiting for courses that are not complete, preparing, or go
                        Text(
                            text = "Waiting",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            fontFamily = fontPoppins,
                            color = Color.Gray,
                            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp)
                        )
                    }
                }
            }
        }
    }

    // Delete Course Confirmation Dialog
    if (showDeleteCourseDialog) {
        androidx.compose.material3.AlertDialog(
            onDismissRequest = { showDeleteCourseDialog = false },
            title = {
                Text(
                    text = "Remove Course",
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins
                )
            },
            text = {
                Text(
                    text = "Do you want to remove this course?\n\n\"$courseName\"\n\nRemoving this course will remove all ${itemCount} items in this course.",
                    fontSize = 16.sp,
                    fontFamily = fontPoppins,
                    color = Color.Black
                )
            },
            confirmButton = {
                androidx.compose.material3.TextButton(
                    onClick = {
                        // remove the course with all items
                        productsScreenViewModel.removeCourse(courseId, state)
                        showDeleteCourseDialog = false
                    }
                ) {
                    Text(
                        text = "Yes, Remove",
                        color = Color.Red,
                        fontWeight = FontWeight.Bold,
                        fontFamily = fontPoppins
                    )
                }
            },
            dismissButton = {
                androidx.compose.material3.TextButton(
                    onClick = { showDeleteCourseDialog = false }
                ) {
                    Text(
                        text = "Cancel",
                        color = Color.Gray,
                        fontWeight = FontWeight.Bold,
                        fontFamily = fontPoppins
                    )
                }
            },
            containerColor = Color.White,
            titleContentColor = Color(0xFF2E7D32),
            textContentColor = Color.Black
        )
    }
}

@Composable
private fun MobileEmptyCart() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        androidx.compose.material3.Icon(
            painter = painterResource(R.drawable.cart_plus),
            contentDescription = "Empty cart",
            modifier = Modifier.size(80.dp),
            tint = Color(0xFF2E7D32).copy(alpha = 0.6f)
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "Your cart is empty",
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            fontFamily = fontPoppins
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = "Add items to get started",
            fontSize = 16.sp,
            color = Color.Black.copy(alpha = 0.7f),
            fontFamily = fontPoppins,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun OrderItemCard(
    cartItemWithCourse: CartItemWithCourse,
    availableCourses: List<MealCourse>,
    onRemoveItem: (Cart) -> Unit,
    onUpdateStock: (Int, StoreItem, Cart) -> Unit,
    onUpdateNotes: (Cart, String) -> Unit,
    onVoidItem: (Cart) -> Unit,
    onCourseChanged: (String) -> Unit,
    openPrefillFromCart: (Cart) -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    val cartItem = cartItemWithCourse.cart
    var showNotesDialog by remember { mutableStateOf(false) }
    var showRemoveDialog by remember { mutableStateOf(false) }
    var itemNotes by remember { mutableStateOf(cartItem.notes ?: "") }

    // Check if this item has been sent to kitchen
    val isSentToKitchen = cartItem.sentToKitchen

    if (showNotesDialog) {
        MobileOrderNotesDialog(
            initialNotes = itemNotes,
            onDismiss = { showNotesDialog = false },
            onConfirm = { notes ->
                itemNotes = notes
                val updateCartItem = cartItem.copy(notes = notes)
                onUpdateNotes(updateCartItem, notes)
                showNotesDialog = false
            }
        )
    }

    // Confirmation dialog for item removal
    if (showRemoveDialog) {
        AlertDialog(
            onDismissRequest = {
                showRemoveDialog = false
            },
            title = {
                Text(
                    text = "Remove Item",
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins
                )
            },
            text = {
                Text(
                    text = "Do you want to remove this item from cart?\n\n${cartItem.storeItem?.name ?: "Unknown Item"}",
                    fontSize = 16.sp,
                    fontFamily = fontPoppins
                )
            },
            confirmButton = {
                androidx.compose.material3.TextButton(
                    onClick = {
                        onRemoveItem(cartItem)
                        showRemoveDialog = false
                    }
                ) {
                    Text(
                        text = "Yes",
                        color = Color(0xFF2E7D32),
                        fontWeight = FontWeight.Bold,
                        fontFamily = fontPoppins
                    )
                }
            },
            dismissButton = {
                androidx.compose.material3.TextButton(
                    onClick = {
                        showRemoveDialog = false
                    }
                ) {
                    Text(
                        text = "No",
                        color = Color.Gray,
                        fontWeight = FontWeight.Bold,
                        fontFamily = fontPoppins
                    )
                }
            },
            containerColor = Color.White,
            titleContentColor = Color(0xFF2E7D32),
            textContentColor = Color.Black
        )
    }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Item details
        Column(
            modifier = Modifier
                .weight(1f)
                .clickable {
                    // Open product details with prefilled options from this cart item
                    openPrefillFromCart(cartItem)
                }
        ) {
            // Product name row with Add Note button and delete icon
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    modifier = if (isMobilePOS) Modifier.width(130.dp) else Modifier.width(250.dp),
                    text = cartItem.storeItem?.name ?: "",
                    fontSize = if (isMobilePOS) 12.sp else 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black,
                    fontFamily = fontPoppins,
                )
            }

            // Show options one by one below the product title
            cartItem.storeItem?.optionSets?.forEach { optionSet ->
                optionSet.options?.forEach { option ->
                    if ((option?.quantity ?: 0) > 0) {
                        Text(
                            text = "Options: x${option?.quantity} - ${option?.name ?: ""}",
                            fontSize = 12.sp,
                            color = Color.Black.copy(alpha = 0.7f),
                            fontFamily = fontPoppins,
                            modifier = Modifier.padding(top = 2.dp)
                        )
                    }
                }
            }

            // Show notes section
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 2.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Notes:",
                    fontSize = 12.sp,
                    color = Color.Black.copy(alpha = 0.7f),
                    fontFamily = fontPoppins,
                    fontWeight = FontWeight.Medium
                )

                if (itemNotes.isNotEmpty()) {
                    Text(
                        text = " $itemNotes",
                        fontSize = 12.sp,
                        color = Color.Black.copy(alpha = 0.7f),
                        fontFamily = fontPoppins,
                        modifier = Modifier.padding(start = 4.dp)
                    )
                    Spacer(modifier = Modifier.padding(start = 8.dp))
                    androidx.compose.material3.TextButton(
                        onClick = { showNotesDialog = true },
                        modifier = Modifier.height(24.dp),
                        contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp)
                    ) {
                        Text(
                            text = "View/Edit",
                            fontSize = 10.sp,
                            fontWeight = FontWeight.Medium,
                            fontFamily = fontPoppins,
                            color = Color(CATEGORY_GREEN_COLOR),
                            textDecoration = TextDecoration.Underline
                        )
                    }
                } else {
                    Spacer(modifier = Modifier.padding(start = 8.dp))
                    Button(
                        onClick = { showNotesDialog = true },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2E7D32),
                            contentColor = Color.White
                        ),
                        shape = RoundedCornerShape(4.dp),
                        modifier = Modifier.height(24.dp),
                        contentPadding = PaddingValues(horizontal = 8.dp, vertical = 2.dp)
                    ) {
                        Text(
                            text = "Add Note",
                            fontSize = 10.sp,
                            fontWeight = FontWeight.Medium,
                            fontFamily = fontPoppins,
                            color = Color.White
                        )
                    }
                }
            }
        }

        // Delete button - only show if not sent to kitchen
        if (!isSentToKitchen) {
            androidx.compose.material3.IconButton(
                onClick = { showRemoveDialog = true },
                modifier = Modifier.size(32.dp)
            ) {
                androidx.compose.material3.Icon(
                    imageVector = Icons.Default.Delete,
                    contentDescription = "Delete item",
                    tint = Color.Red,
                    modifier = Modifier.size(16.dp)
                )
            }
        }

        // Quantity controls - only show if not sent to kitchen
        if (!isSentToKitchen) {
            MobileStockUpdateCounterCart(
                initialStock = cartItem.quantity ?: 1
            ) { stock ->
                onUpdateStock(stock, cartItem.storeItem ?: StoreItem(), cartItem)
            }
        } else {
            // Show quantity as text when sent to kitchen
            Text(
                text = "Qty: ${cartItem.quantity ?: 1}",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Gray,
                fontFamily = fontPoppins,
                modifier = Modifier.padding(horizontal = 16.dp)
            )
        }

        // Price
        Text(
            text = "£${cartItem.netPayable?.transformDecimal() ?: "0.00"}",
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            fontFamily = fontPoppins,
            modifier = Modifier.padding(start = 16.dp)
        )
    }
}

@Composable
private fun MobileCartItemCardWithCourse(
    cartItemWithCourse: CartItemWithCourse,
    availableCourses: List<MealCourse>,
    onRemoveItem: (Cart) -> Unit,
    onUpdateStock: (Int, StoreItem, Cart) -> Unit,
    onUpdateNotes: (Cart, String) -> Unit,
    onVoidItem: (Cart) -> Unit,
    onCourseChanged: (String) -> Unit,
    openPrefillFromCart : (Cart) -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    val cartItem = cartItemWithCourse.cart
    var showNotesDialog by remember { mutableStateOf(false) }
    var showCourseDialog by remember { mutableStateOf(false) }
    var showRemoveDialog by remember { mutableStateOf(false) }
    var itemNotes by remember { mutableStateOf(cartItem.notes ?: "") }

    // Check if this item has been sent to kitchen
    val isSentToKitchen = cartItem.sentToKitchen

    if (showNotesDialog) {
        MobileOrderNotesDialog(
            initialNotes = itemNotes,
            onDismiss = { showNotesDialog = false },
            onConfirm = { notes ->
                itemNotes = notes
                val updateCartItem = cartItem.copy(notes = notes)
                onUpdateNotes(updateCartItem, notes)
                showNotesDialog = false
            }
        )
    }

    if (showCourseDialog) {
        MealCourseSelectionDialog(
            currentCourseId = cartItemWithCourse.courseId,
            availableCourses = availableCourses,
            onDismiss = { showCourseDialog = false },
            onCourseSelected = { courseId ->
                onCourseChanged(courseId)
                showCourseDialog = false
            }
        )
    }

    // Confirmation dialog for item removal
    if (showRemoveDialog) {
        AlertDialog(
            onDismissRequest = {
                showRemoveDialog = false
            },
            title = {
                Text(
                    text = "Remove Item",
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins
                )
            },
            text = {
                Text(
                    text = "Do you want to remove this item from cart?\n\n${cartItem.storeItem?.name ?: "Unknown Item"}",
                    fontSize = 16.sp,
                    fontFamily = fontPoppins
                )
            },
            confirmButton = {
                androidx.compose.material3.TextButton(
                    onClick = {
                        onRemoveItem(cartItem)
                        showRemoveDialog = false
                    }
                ) {
                    Text(
                        text = "Yes",
                        color = Color(0xFF2E7D32),
                        fontWeight = FontWeight.Bold,
                        fontFamily = fontPoppins
                    )
                }
            },
            dismissButton = {
                androidx.compose.material3.TextButton(
                    onClick = {
                        showRemoveDialog = false
                    }
                ) {
                    Text(
                        text = "No",
                        color = Color.Gray,
                        fontWeight = FontWeight.Bold,
                        fontFamily = fontPoppins
                    )
                }
            },
            containerColor = Color.White,
            titleContentColor = Color(0xFF2E7D32),
            textContentColor = Color.Black
        )
    }

    androidx.compose.material3.Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Course indicator badge
            val currentCourse = availableCourses.find { it.name == cartItemWithCourse.courseId }
            androidx.compose.material3.Card(
                modifier = Modifier
                    .padding(bottom = 8.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xFF2E7D32).copy(alpha = 0.1f)
                ),
                shape = RoundedCornerShape(6.dp)
            ) {
                Text(
                    text = currentCourse?.name ?: "Course 1",
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins,
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                )
            }

            // Header with item name and remove button
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f).clickable {
                    openPrefillFromCart(cartItem)
                }) {
                    Text(
                        text = cartItem.storeItem?.name?.uppercase() ?: "UNKNOWN",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Black,
                        fontFamily = fontPoppins
                    )

                    // Action buttons row
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        androidx.compose.material3.TextButton(
                            onClick = { showNotesDialog = true },
                            contentPadding = PaddingValues(0.dp)
                        ) {
                            Text(
                                text = if (itemNotes.isNotEmpty()) "Edit Note" else "Add Note",
                                fontSize = 14.sp,
                                color = Color(0xFF2E7D32),
                                fontFamily = fontPoppins
                            )
                        }

                        androidx.compose.material3.TextButton(
                            onClick = { showCourseDialog = true },
                            contentPadding = PaddingValues(0.dp)
                        ) {
                            val currentCourse =
                                availableCourses.find { it.name == cartItemWithCourse.courseId }
                            Text(
                                text = "Meal: ${currentCourse?.name ?: "Course 1"}",
                                fontSize = 14.sp,
                                color = Color(0xFF2E7D32),
                                fontFamily = fontPoppins
                            )
                        }
                    }
                }

                // Delete button - only show if not sent to kitchen
                if (!isSentToKitchen) {
                    androidx.compose.material3.IconButton(
                        onClick = { showRemoveDialog = true },
                        modifier = Modifier.size(40.dp)
                    ) {
                        androidx.compose.material3.Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "Remove item",
                            tint = Color.Red
                        )
                    }
                    //productsScreenViewModel.prefs.storeConfigurations?.data?.quickService != true
                    if (true) {
                        Spacer(modifier = Modifier.width(8.dp))
                        androidx.compose.material3.TextButton(
                            onClick = { onVoidItem(cartItem) },
                        ) {
                            Text(
                                text = "Void Item",
                                fontSize = 14.sp,
                                color = Color.Red,
                                fontFamily = fontPoppins
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // Item details
            cartItem.storeItem?.let { storeItem ->
                MobileCartItemDetails(storeItem = storeItem, cartQuantity = cartItem.quantity ?: 1)
            }

            // Notes section
            if (itemNotes.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                androidx.compose.material3.Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFF5F5F5)
                    )
                ) {
                    Column(modifier = Modifier.padding(8.dp)) {
                        Text(
                            text = "Notes:",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color.Black,
                            fontFamily = fontPoppins
                        )
                        Text(
                            text = itemNotes,
                            fontSize = 12.sp,
                            color = Color.Black.copy(alpha = 0.8f),
                            fontStyle = FontStyle.Italic,
                            fontFamily = fontPoppins,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Quantity and price section
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Quantity controls - only show if not sent to kitchen
                if (!isSentToKitchen) {
                    MobileStockUpdateCounterCart(
                        initialStock = cartItem.quantity ?: 1
                    ) { stock ->
                        onUpdateStock(stock, cartItem.storeItem ?: StoreItem(), cartItem)
                    }
                } else {
                    // Show quantity as text when sent to kitchen
                    Text(
                        text = "Qty: ${cartItem.quantity ?: 1}",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.Gray,
                        fontFamily = fontPoppins
                    )
                }

                Text(
                    text = "£${cartItem.netPayable?.transformDecimal() ?: "0.00"}",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins
                )
            }
        }
    }
}

@Composable
private fun MobileCartItemDetails(storeItem: StoreItem, cartQuantity: Int) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // Main item - use cart quantity instead of store item quantity
        MobileCartItemRow(
            quantity = cartQuantity,
            name = storeItem.name ?: "",
            price = (storeItem.price ?: 0.0) * cartQuantity
        )

        // Options
        storeItem.optionSets?.forEach { optionSet ->
            optionSet.options?.forEach { option ->
                if ((option?.quantity ?: 0) > 0) {
                    MobileCartItemRow(
                        quantity = option?.quantity ?: 0,
                        name = option?.name ?: "",
                        price = (option?.price ?: 0.0) * (option?.quantity ?: 0) * cartQuantity,
                        isExtra = true
                    )
                }
            }
        }
    }
}

@Composable
private fun MobileCartItemRow(
    quantity: Int,
    name: String,
    price: Double,
    isExtra: Boolean = false
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "${if (isExtra) "  + " else ""}$quantity x $name",
            fontSize = 14.sp,
            color = if (isExtra) Color.Black.copy(alpha = 0.7f) else Color.Black,
            fontFamily = fontPoppins,
            modifier = Modifier.weight(1f)
        )

        Text(
            text = "£${price.transformDecimal()}",
            fontSize = 14.sp,
            color = Color.Black,
            fontWeight = if (isExtra) FontWeight.Normal else FontWeight.Medium,
            fontFamily = fontPoppins
        )
    }
}

@Composable
private fun OrderSummarySection(
    order: Order,
    onCourseBillClick: (Order) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(color = Color.White)
            .padding(bottom = 30.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // Total Payable with green background
        androidx.compose.material3.Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFF2E7D32)
            ),
            shape = RoundedCornerShape(8.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Sub Total",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    fontFamily = fontPoppins
                )
                Text(
                    text = "£${order.netPayable?.transformDecimal() ?: "0.00"}",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    fontFamily = fontPoppins
                )
            }
        }
    }
}

@Composable
private fun MobileCartSummary(order: Order) {
    androidx.compose.material3.Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "Order Summary",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF2E7D32),
                fontFamily = fontPoppins
            )

            MobileSummaryRow(
                title = "Items (${order.carts?.size ?: 0})",
                value = "£${order.netPayable?.transformDecimal() ?: "0.00"}"
            )

            MobileSummaryRow(
                title = "Taxes",
                value = "£${order.tax?.transformDecimal() ?: "0.00"}"
            )

            HorizontalDivider(
                modifier = Modifier.padding(vertical = 4.dp),
                color = Color.Black.copy(alpha = 0.2f)
            )

            MobileSummaryRow(
                title = "Total Payable",
                value = "£${order.totalPrice?.transformDecimal() ?: "0.00"}",
                isBold = true,
                valueColor = Color(0xFF2E7D32)
            )
        }
    }
}

@Composable
private fun MobileSummaryRow(
    title: String,
    value: String,
    isBold: Boolean = false,
    valueColor: Color = Color.Black
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            fontSize = 14.sp,
            fontWeight = if (isBold) FontWeight.Bold else FontWeight.Normal,
            color = Color.Black,
            fontFamily = fontPoppins
        )

        Text(
            text = value,
            fontSize = 14.sp,
            fontWeight = if (isBold) FontWeight.Bold else FontWeight.Normal,
            color = valueColor,
            fontFamily = fontPoppins
        )
    }
}

@Composable
fun MobileOrderTotalSticky(
    order: Order,
    placeOrderCash: () -> Unit,
    placeOrderCard: () -> Unit,
    onSplitBillClick: (Int) -> Unit,
    onCloudPrintClick: (Order) -> Unit,
    onSyncTable: () -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    val ordersResponse by productsScreenViewModel.collectAsState(ProductsScreenState::orderResponse)
    var showSplitBillDialog by remember { mutableStateOf(false) }

    if (showSplitBillDialog) {
        SplitBillDialog(
            onDismiss = { showSplitBillDialog = false },
            onConfirm = { numberOfPersons ->
                showSplitBillDialog = false
                onSplitBillClick(numberOfPersons)
            }
        )
    }

    androidx.compose.material3.Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(start = 16.dp, end = 16.dp, top = 16.dp, bottom = 48.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Total amount
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "Total",
                        fontSize = 16.sp,
                        color = Color.Black.copy(alpha = 0.7f),
                        fontFamily = fontPoppins
                    )
                    Text(
                        text = "£${order.totalPrice?.transformDecimal() ?: "0.00"}",
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF2E7D32),
                        fontFamily = fontPoppins
                    )
                }
            }

            // Payment buttons
            if (ordersResponse is Loading) {
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = Color(0xFF2E7D32)
                    )
                }
            } else {
                Column(
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {

                    //productsScreenViewModel.prefs.storeConfigurations?.data?.quickService != true
                    // Split Bill Button
                    if (true) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            androidx.compose.material3.OutlinedButton(
                                onClick = {
                                    onCloudPrintClick(order)
                                },
                                modifier = Modifier
                                    .weight(1f)
                                    .height(56.dp),
                                shape = RoundedCornerShape(12.dp),
                                border = BorderStroke(2.dp, Color(0xFF2E7D32)),
                                colors = ButtonDefaults.outlinedButtonColors(
                                    contentColor = Color(0xFF2E7D32)
                                )
                            ) {
                                androidx.compose.material3.Icon(
                                    imageVector = Icons.Default.AddCard,
                                    contentDescription = null,
                                    modifier = Modifier.size(20.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "Cloud Print",
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 16.sp,
                                    fontFamily = fontPoppins
                                )
                            }

                            Button(
                                onClick = {
                                    showSplitBillDialog = true
                                },
                                modifier = Modifier
                                    .weight(1f)
                                    .height(56.dp),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFF2E7D32),
                                    contentColor = Color.White
                                ),
                                shape = RoundedCornerShape(12.dp)
                            ) {
                                androidx.compose.material3.Icon(
                                    imageVector = Icons.Default.Money,
                                    contentDescription = null,
                                    modifier = Modifier.size(20.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "Split Bill Payment",
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 16.sp,
                                    fontFamily = fontPoppins,
                                    color = Color.White
                                )
                            }
//                            androidx.compose.material3.OutlinedButton(
//                                onClick = {
//                                    onCloudPrintClick(order)
//                                },
//                                modifier = Modifier
//                                    .weight(1f)
//                                    .height(56.dp),
//                                shape = RoundedCornerShape(12.dp),
//                                border = BorderStroke(2.dp, Color(0xFF2E7D32)),
//                                colors = ButtonDefaults.outlinedButtonColors(
//                                    contentColor = Color(0xFF2E7D32)
//                                )
//                            ) {
//                                androidx.compose.material3.Icon(
//                                    imageVector = Icons.Default.AddCard,
//                                    contentDescription = null,
//                                    modifier = Modifier.size(20.dp)
//                                )
//                                Spacer(modifier = Modifier.width(8.dp))
//                                Text(
//                                    text = "Cloud Print",
//                                    fontWeight = FontWeight.Bold,
//                                    fontSize = 16.sp,
//                                    fontFamily = fontPoppins
//                                )
//                            }
//                            Button(
//                                onClick = { showSplitBillDialog = true },
//                                modifier = Modifier
//                                    .fillMaxWidth()
//                                    .height(56.dp),
//                                colors = ButtonDefaults.buttonColors(
//                                    containerColor = Color(0xFF2E7D32),
//                                    contentColor = Color.White
//                                ),
//                                shape = RoundedCornerShape(12.dp)
//                            ) {
//                                androidx.compose.material3.Icon(
//                                    imageVector = Icons.Default.Splitscreen,
//                                    contentDescription = null,
//                                    modifier = Modifier.size(20.dp)
//                                )
//                                Spacer(modifier = Modifier.width(8.dp))
//                                Text(
//                                    text = "Split Bill Payment",
//                                    fontWeight = FontWeight.Bold,
//                                    fontSize = 16.sp,
//                                    fontFamily = fontPoppins,
//                                    color = Color.White
//                                )
//                            }
                        }
                    }

                    // Regular Payment Buttons
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        androidx.compose.material3.OutlinedButton(
                            onClick = placeOrderCard,
                            modifier = Modifier
                                .weight(1f)
                                .height(56.dp),
                            shape = RoundedCornerShape(12.dp),
                            border = BorderStroke(2.dp, Color(0xFF2E7D32)),
                            colors = ButtonDefaults.outlinedButtonColors(
                                contentColor = Color(0xFF2E7D32)
                            )
                        ) {
                            androidx.compose.material3.Icon(
                                imageVector = Icons.Default.AddCard,
                                contentDescription = null,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "Card",
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp,
                                fontFamily = fontPoppins
                            )
                        }

                        Button(
                            onClick = placeOrderCash,
                            modifier = Modifier
                                .weight(1f)
                                .height(56.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF2E7D32),
                                contentColor = Color.White
                            ),
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            androidx.compose.material3.Icon(
                                imageVector = Icons.Default.Money,
                                contentDescription = null,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "Cash",
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp,
                                fontFamily = fontPoppins,
                                color = Color.White
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun MobileStockUpdateCounterCart(
    initialStock: Int = 1,
    onStockChange: (Int) -> Unit,
) {
    var stock = initialStock

    Row(
        modifier = Modifier
            .background(
                Color(0xFF2E7D32),
                RoundedCornerShape(8.dp)
            )
            .padding(4.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        androidx.compose.material3.IconButton(
            onClick = {
                if (stock > 1) {
                    stock--
                    onStockChange(stock)
                }
            },
            modifier = Modifier.size(32.dp)
        ) {
            androidx.compose.material3.Icon(
                imageVector = Icons.Default.RemoveCircle,
                contentDescription = "Decrease",
                modifier = Modifier.size(20.dp),
                tint = Color.White
            )
        }

        Text(
            text = stock.toString(),
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(horizontal = 8.dp),
            color = Color.White,
            fontFamily = fontPoppins
        )

        androidx.compose.material3.IconButton(
            onClick = {
                stock++
                onStockChange(stock)
            },
            modifier = Modifier.size(32.dp)
        ) {
            androidx.compose.material3.Icon(
                imageVector = Icons.Default.AddCircle,
                contentDescription = "Increase",
                modifier = Modifier.size(20.dp),
                tint = Color.White
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MobileOrderNotesDialog(
    initialNotes: String,
    onDismiss: () -> Unit,
    onConfirm: (String) -> Unit
) {
    var notes by remember { mutableStateOf(initialNotes) }

    Dialog(onDismissRequest = onDismiss) {
        androidx.compose.material3.Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Add Order Notes",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                OutlinedTextField(
                    value = notes,
                    onValueChange = { notes = it },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    placeholder = {
                        Text(
                            "Enter notes for this item...",
                            color = Color.Black.copy(alpha = 0.6f),
                            fontFamily = fontPoppins
                        )
                    },
                    maxLines = 5,
                    shape = RoundedCornerShape(8.dp),
                    colors = TextFieldDefaults.outlinedTextFieldColors(
                        focusedBorderColor = Color(0xFF2E7D32),
                        unfocusedBorderColor = Color.Black.copy(alpha = 0.3f),
                        focusedTextColor = Color.Black,
                        unfocusedTextColor = Color.Black
                    )
                )

                Spacer(modifier = Modifier.height(16.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    androidx.compose.material3.TextButton(
                        onClick = onDismiss,
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = Color.Black.copy(alpha = 0.7f)
                        )
                    ) {
                        Text(
                            "Cancel",
                            fontFamily = fontPoppins
                        )
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    androidx.compose.material3.TextButton(
                        onClick = { onConfirm(notes) },
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = Color(0xFF2E7D32)
                        )
                    ) {
                        Text(
                            "Save",
                            fontWeight = FontWeight.Medium,
                            fontFamily = fontPoppins
                        )
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AddNewCourseDialog(
    onDismiss: () -> Unit,
    onConfirm: (String) -> Unit
) {
    var courseName by remember { mutableStateOf("") }

    Dialog(onDismissRequest = onDismiss) {
        androidx.compose.material3.Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Add New Course",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                OutlinedTextField(
                    value = courseName,
                    onValueChange = { courseName = it },
                    label = {
                        Text(
                            text = "Course Name",
                            fontFamily = fontPoppins
                        )
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    colors = TextFieldDefaults.outlinedTextFieldColors(
                        focusedBorderColor = Color(0xFF2E7D32),
                        focusedLabelColor = Color(0xFF2E7D32),
                        cursorColor = Color(0xFF2E7D32)
                    ),
                    singleLine = true
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    androidx.compose.material3.OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color.Gray
                        )
                    ) {
                        Text(
                            text = "Cancel",
                            fontFamily = fontPoppins
                        )
                    }

                    Button(
                        onClick = {
                            if (courseName.isNotBlank()) {
                                onConfirm(courseName.trim())
                            }
                        },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2E7D32),
                            contentColor = Color.White
                        ),
                        enabled = courseName.isNotBlank()
                    ) {
                        Text(
                            text = "Add Course",
                            fontFamily = fontPoppins,
                            color = Color.White
                        )
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MealCourseSelectionDialog(
    currentCourseId: String,
    availableCourses: List<MealCourse>,
    onDismiss: () -> Unit,
    onCourseSelected: (String) -> Unit
) {
    Dialog(onDismissRequest = onDismiss) {
        androidx.compose.material3.Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Select Meal Course",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                // Course selection options
                availableCourses.forEach { course ->
                    val isSelected = course.name == currentCourseId
                    val backgroundColor = if (isSelected) Color(0xFF2E7D32) else Color.White
                    val textColor = if (isSelected) Color.White else Color(0xFF2E7D32)
                    val borderColor = Color(0xFF2E7D32)

                    androidx.compose.material3.Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp)
                            .clickable { onCourseSelected(course.name) }
                            .border(
                                width = 2.dp,
                                color = borderColor,
                                shape = RoundedCornerShape(8.dp)
                            ),
                        shape = RoundedCornerShape(8.dp),
                        colors = CardDefaults.cardColors(containerColor = backgroundColor),
                        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                    ) {
                        Text(
                            text = course.name,
                            color = textColor,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            fontFamily = fontPoppins,
                            modifier = Modifier.padding(16.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Cancel button
                androidx.compose.material3.TextButton(
                    onClick = onDismiss,
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = Color.Black.copy(alpha = 0.7f)
                    )
                ) {
                    Text(
                        "Cancel",
                        fontFamily = fontPoppins
                    )
                }
            }
        }
    }
}

@Composable
private fun MobileCartItemCard(
    cartItem: Cart,
    onRemoveItem: (Cart) -> Unit,
    onUpdateStock: (Int, StoreItem, Cart) -> Unit,
    onUpdateNotes: (Cart, String) -> Unit,
    onVoidItem: (Cart) -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    val state by productsScreenViewModel.collectAsState()

    // Get state from ViewModel
    val availableCourses = state.availableCourses
    val cartItemsWithCourses = state.getCurrentTableCartItemsWithCourses()

    // Find the course assignment for this cart item, default to Course 1
    val cartItemWithCourse =
        cartItemsWithCourses.find { it.cart.uuid == cartItem.uuid }
            ?: CartItemWithCourse(cart = cartItem, courseId = "", courseStatus = 0)

    MobileCartItemCardWithCourse(
        cartItemWithCourse = cartItemWithCourse,
        availableCourses = availableCourses,
        onRemoveItem = onRemoveItem,
        onUpdateStock = onUpdateStock,
        onUpdateNotes = onUpdateNotes,
        onVoidItem = onVoidItem,
        onCourseChanged = { newCourseId ->
            productsScreenViewModel.updateCartItemCourse(
                cartUuid = cartItem.uuid,
                newCourseId = newCourseId
            )
        },
        openPrefillFromCart = {

        },
        productsScreenViewModel = productsScreenViewModel
    )
}

@Composable
fun StockUpdateCounterCart(
    initialStock: Int = 1, onStockChange: (Int) -> Unit
) {
    var stock by remember { mutableStateOf(initialStock) }

    Row(
        modifier = Modifier
            .padding(8.dp)
            .wrapContentWidth()
            .border(
                width = 1.dp,
                color = Color(0xeFFbf0ff),
                shape = RoundedCornerShape(1.dp)
            ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        androidx.compose.material3.IconButton(onClick = {
            if (stock > 1) {
                stock--
                onStockChange(stock)
            }
        }) {
            androidx.compose.material3.Icon(
                modifier = Modifier
                    .background(Color.White)
                    .padding(8.dp),
                painter = painterResource(id = R.drawable.cart_minus),
                contentDescription = "Decrease"
            )
        }

        Text(
            text = stock.toString(), style = TextStyle(
                fontFamily = fontPoppins,
                fontWeight = FontWeight.Normal,
                fontSize = 12.sp
            ), modifier = Modifier
                .background(Color(0xFFebf0ff))
                .padding(8.dp)
                .width(48.dp),
            textAlign = TextAlign.Center
        )

        androidx.compose.material3.IconButton(onClick = {
            stock++
            onStockChange(stock)
        }) {
            androidx.compose.material3.Icon(
                modifier = Modifier
                    .background(Color.White)
                    .padding(8.dp),
                painter = painterResource(id = R.drawable.cart_plus),
                contentDescription = "Decrease"
            )
        }
    }
}

@Composable
fun OrderNotesDialog(
    initialNotes: String,
    onDismiss: () -> Unit,
    onConfirm: (String) -> Unit
) {
    var notes by remember { mutableStateOf(initialNotes) }

    Dialog(onDismissRequest = {
        onDismiss()
    }) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Add Order Notes",
                    style = TextStyle(
                        fontFamily = fontPoppins,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold
                    ),
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                TextField(
                    value = notes,
                    onValueChange = { notes = it },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    placeholder = { Text("Enter notes for this item...") },
                    maxLines = 5
                )

                Spacer(modifier = Modifier.height(16.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("Cancel")
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    TextButton(
                        onClick = { onConfirm(notes) },
                        colors = androidx.compose.material.ButtonDefaults.textButtonColors(
                            contentColor = Color(0xFF40bfff)
                        )
                    ) {
                        Text("Save")
                    }
                }
            }
        }
    }
}

@Composable
fun TotalCartFigma(
    title: String, value: String, style: TextStyle = TextStyle(
        fontFamily = fontNunito,
        fontSize = 14.sp,
        fontWeight = FontWeight.Normal,
    ), isBold: Boolean = false
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        androidx.compose.material3.Text(
            modifier = Modifier.width(180.dp),
            text = title,
            style = style,
            fontWeight = if (isBold) FontWeight.Bold else FontWeight.Normal
        )
        androidx.compose.material3.Text(
            modifier = Modifier.width(80.dp),
            text = value,
            style = style,
            fontWeight = if (isBold) FontWeight.Bold else FontWeight.Normal
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SplitBillDialog(
    onDismiss: () -> Unit,
    onConfirm: (Int) -> Unit
) {
    var numberOfPersons by remember { mutableStateOf("") }
    var isError by remember { mutableStateOf(false) }

    Dialog(onDismissRequest = onDismiss) {
        androidx.compose.material3.Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "Split Bill Payment",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins
                )

                Text(
                    text = "Enter the number of persons to split the bill",
                    fontSize = 16.sp,
                    color = Color.Black.copy(alpha = 0.7f),
                    fontFamily = fontPoppins,
                    textAlign = TextAlign.Center
                )

                OutlinedTextField(
                    value = numberOfPersons,
                    onValueChange = { newValue ->
                        // Only allow numeric input
                        if (newValue.isEmpty() || (newValue.all { it.isDigit() } && newValue.toIntOrNull() != null)) {
                            numberOfPersons = newValue
                            // Validate the number
                            val number = newValue.toIntOrNull()
                            isError = number != null && (number < 1 || number > 20)
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    label = {
                        Text(
                            "Number of Persons",
                            fontFamily = fontPoppins
                        )
                    },
                    placeholder = {
                        Text(
                            "Enter number (1-20)",
                            color = Color.Black.copy(alpha = 0.6f),
                            fontFamily = fontPoppins
                        )
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    isError = isError,
                    supportingText = if (isError) {
                        {
                            Text(
                                "Number must be between 1 and 20",
                                color = Color.Red,
                                fontFamily = fontPoppins
                            )
                        }
                    } else null,
                    shape = RoundedCornerShape(8.dp),
                    colors = TextFieldDefaults.outlinedTextFieldColors(
                        focusedBorderColor = Color(0xFF2E7D32),
                        unfocusedBorderColor = Color.Black.copy(alpha = 0.3f),
                        focusedTextColor = Color.Black,
                        unfocusedTextColor = Color.Black,
                        focusedLabelColor = Color(0xFF2E7D32),
                        unfocusedLabelColor = Color.Black.copy(alpha = 0.6f)
                    )
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    androidx.compose.material3.OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp),
                        shape = RoundedCornerShape(8.dp),
                        border = BorderStroke(1.dp, Color.Black.copy(alpha = 0.3f)),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color.Black.copy(alpha = 0.7f)
                        )
                    ) {
                        Text(
                            "Cancel",
                            fontFamily = fontPoppins,
                            fontWeight = FontWeight.Medium
                        )
                    }

                    Button(
                        onClick = {
                            val number = numberOfPersons.toIntOrNull()
                            if (number != null && number in 1..20) {
                                onConfirm(number)
                            }
                        },
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp),
                        enabled = numberOfPersons.isNotEmpty() && !isError && numberOfPersons.toIntOrNull() != null,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2E7D32),
                            contentColor = Color.White,
                            disabledContainerColor = Color.Gray.copy(alpha = 0.3f),
                            disabledContentColor = Color.Gray
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text(
                            "Open Split Bill Screen",
                            fontWeight = FontWeight.Bold,
                            fontFamily = fontPoppins
                        )
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun EditCoursesDialog(
    availableCourses: List<MealCourse>,
    onDismiss: () -> Unit,
    onAddCourse: (String) -> Unit,
    onEditCourse: (String, String) -> Unit,
    onRemoveCourse: (String) -> Unit
) {
    var showAddCourseDialog by remember { mutableStateOf(false) }
    var editingCourse by remember { mutableStateOf<MealCourse?>(null) }
    var editCourseName by remember { mutableStateOf("") }

    Dialog(onDismissRequest = onDismiss) {
        androidx.compose.material3.Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                // Title
                Text(
                    text = "Edit Courses",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                // Course list
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(300.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(availableCourses) { course ->
                        CourseEditItem(
                            course = course,
                            onEdit = {
                                editingCourse = course
                                editCourseName = course.name
                            },
                            onRemove = { onRemoveCourse(course.name) }
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Action buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    androidx.compose.material3.TextButton(
                        onClick = { showAddCourseDialog = true },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "Add Course",
                            color = Color(0xFF2E7D32),
                            fontWeight = FontWeight.Bold
                        )
                    }

                    androidx.compose.material3.TextButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "Done",
                            color = Color(0xFF2E7D32),
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }
    }

    // Add Course Dialog
    if (showAddCourseDialog) {
        AddNewCourseDialog(
            onDismiss = { showAddCourseDialog = false },
            onConfirm = { courseName ->
                onAddCourse(courseName)
                showAddCourseDialog = false
            }
        )
    }

    // Edit Course Dialog
    editingCourse?.let { course ->
        EditCourseNameDialog(
            currentName = editCourseName,
            onDismiss = {
                editingCourse = null
                editCourseName = ""
            },
            onConfirm = { newName ->
                onEditCourse(course.name, newName)
                editingCourse = null
                editCourseName = ""
            },
            onNameChange = { editCourseName = it }
        )
    }
}

@Composable
private fun CourseEditItem(
    course: MealCourse,
    onEdit: () -> Unit,
    onRemove: () -> Unit
) {
    androidx.compose.material3.Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color(0xFF2E7D32).copy(alpha = 0.1f)),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = course.name,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Black,
                fontFamily = fontPoppins,
                modifier = Modifier.weight(1f)
            )

            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                IconButton(
                    onClick = onEdit,
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = "Edit Course",
                        tint = Color(0xFF2E7D32),
                        modifier = Modifier.size(18.dp)
                    )
                }

                IconButton(
                    onClick = onRemove,
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "Remove Course",
                        tint = Color(0xFFD32F2F),
                        modifier = Modifier.size(18.dp)
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun EditCourseNameDialog(
    currentName: String,
    onDismiss: () -> Unit,
    onConfirm: (String) -> Unit,
    onNameChange: (String) -> Unit
) {
    Dialog(onDismissRequest = onDismiss) {
        androidx.compose.material3.Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                Text(
                    text = "Edit Course Name",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                OutlinedTextField(
                    value = currentName,
                    onValueChange = onNameChange,
                    label = { Text("Course Name") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )

                Spacer(modifier = Modifier.height(16.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    androidx.compose.material3.TextButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "Cancel",
                            color = Color.Gray,
                            fontWeight = FontWeight.Bold
                        )
                    }

                    androidx.compose.material3.TextButton(
                        onClick = {
                            if (currentName.isNotBlank()) {
                                onConfirm(currentName.trim())
                            }
                        },
                        modifier = Modifier.weight(1f),
                        enabled = currentName.isNotBlank()
                    ) {
                        Text(
                            text = "Save",
                            color = if (currentName.isNotBlank()) Color(0xFF2E7D32) else Color.Gray,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }
    }
}
